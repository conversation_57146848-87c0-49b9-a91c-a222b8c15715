import * as admin from "firebase-admin";
import { TxType, UserTxEntity } from "../types";
import { log } from "../utils/logger";

const USER_TX_HISTORY_COLLECTION = "user_tx_history";

export interface CreateTransactionParams {
  userId: string;
  txType: TxType;
  amount: number;
  orderId?: string;
  description?: string;
}

export async function createTransactionRecord(
  params: CreateTransactionParams
): Promise<void> {
  const { userId, txType, amount, orderId, description } = params;

  try {
    const db = admin.firestore();
    const txRecord: UserTxEntity = {
      tx_type: txType,
      user_id: userId,
      amount,
      order_id: orderId,
      description,
      createdAt:
        admin.firestore.FieldValue.serverTimestamp() as admin.firestore.Timestamp,
    };

    await db.collection(USER_TX_HISTORY_COLLECTION).add(txRecord);

    log.info("Transaction history record created", {
      userId,
      txType,
      amount,
      orderId,
      operation: "create_transaction_record",
    });
  } catch (error) {
    log.error("Error creating transaction history record", error, {
      userId,
      txType,
      amount,
      orderId,
      operation: "create_transaction_record",
    });
    // Don't throw error to avoid breaking main operations
    // Transaction history is supplementary data
  }
}
