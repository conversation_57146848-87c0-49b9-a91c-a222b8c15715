import type { TxType } from '@/constants/core.constants';

/**
 * Standardized transaction amount sign rules:
 * - DEPOSIT: Always negative (-) - money leaving user's wallet to platform
 * - WITHDRAW: Always positive (+) - money returning to user's wallet from platform
 * - BUY_LOCK_COLLATERAL: Always negative (-) - buyer's collateral locked when creating buy order
 * - UNLOCK_COLLATERAL: Always positive (+) - collateral returned to user
 * - SELL_LOCK_COLLATERAL: Always negative (-) - seller's collateral locked when creating sell order
 * - REFERRAL_FEE: Always positive (+) - earnings received by referrer
 * - CANCELATION_FEE: Context-dependent sign:
 *   - Positive (+) when receiving compensation for counterparty cancellation
 *   - Negative (-) when paying penalty for own cancellation
 * - REFUND: Always positive (+) - money returned to user
 * - SELL_FULFILLMENT: Always positive (+) - seller receiving buyer's payment upon order completion
 * - RESELL_FEE_EARNINGS: Always positive (+) - earnings from reselling an order
 */

/**
 * Apply standardized sign to transaction amount based on transaction type
 * @param amount - The absolute amount (always positive input)
 * @param txType - The transaction type
 * @param isReceivingCompensation - For CANCELATION_FEE only: true if receiving compensation, false if paying penalty
 * @returns The amount with correct sign applied
 */
export function applyTransactionSign(
  amount: number,
  txType: TxType,
  isReceivingCompensation?: boolean,
): number {
  if (amount < 0) {
    throw new Error('Amount must be positive when applying transaction sign');
  }

  switch (txType) {
    case 'deposit':
    case 'buy_lock_collateral':
    case 'sell_lock_collateral':
      return -amount; // Money leaving user

    case 'withdraw':
    case 'unlock_collateral':
    case 'referral_fee':
    case 'refund':
    case 'sell_fulfillment':
    case 'resell_fee_earnings':
      return amount; // Money coming to user

    case 'cancelation_fee':
      if (isReceivingCompensation === undefined) {
        throw new Error(
          'isReceivingCompensation must be specified for CANCELATION_FEE transactions',
        );
      }
      return isReceivingCompensation ? amount : -amount;

    default:
      throw new Error(`Unknown transaction type: ${txType}`);
  }
}

/**
 * Check if a transaction type should have a positive amount
 * @param txType - The transaction type
 * @returns true if the transaction should be positive, false if negative, null if context-dependent
 */
export function shouldTransactionBePositive(txType: TxType): boolean | null {
  switch (txType) {
    case 'withdraw':
    case 'unlock_collateral':
    case 'referral_fee':
    case 'refund':
    case 'sell_fulfillment':
    case 'resell_fee_earnings':
      return true;

    case 'deposit':
    case 'buy_lock_collateral':
    case 'sell_lock_collateral':
      return false;

    case 'cancelation_fee':
      return null; // Context-dependent

    default:
      throw new Error(`Unknown transaction type: ${txType}`);
  }
}

/**
 * Validate that a transaction amount has the correct sign for its type
 * @param amount - The transaction amount
 * @param txType - The transaction type
 * @param isReceivingCompensation - For CANCELATION_FEE only: true if receiving compensation
 * @returns true if the sign is correct, false otherwise
 */
export function validateTransactionSign(
  amount: number,
  txType: TxType,
  isReceivingCompensation?: boolean,
): boolean {
  const expectedSign = shouldTransactionBePositive(txType);

  if (expectedSign === null) {
    // Context-dependent (CANCELATION_FEE)
    if (isReceivingCompensation === undefined) {
      return false;
    }
    return isReceivingCompensation ? amount > 0 : amount < 0;
  }

  return expectedSign ? amount > 0 : amount < 0;
}

/**
 * Get a human-readable description of the expected sign for a transaction type
 * @param txType - The transaction type
 * @returns Description of when the transaction should be positive or negative
 */
export function getTransactionSignDescription(txType: TxType): string {
  switch (txType) {
    case 'deposit':
      return "Always negative (-) - money leaving user's wallet to platform";
    case 'withdraw':
      return "Always positive (+) - money returning to user's wallet from platform";
    case 'buy_lock_collateral':
      return "Always negative (-) - buyer's collateral locked when creating buy order";
    case 'unlock_collateral':
      return 'Always positive (+) - collateral returned to user';
    case 'sell_lock_collateral':
      return "Always negative (-) - seller's collateral locked when creating sell order";
    case 'referral_fee':
      return 'Always positive (+) - earnings received by referrer';
    case 'cancelation_fee':
      return 'Context-dependent: positive (+) when receiving compensation, negative (-) when paying penalty';
    case 'refund':
      return 'Always positive (+) - money returned to user';
    case 'sell_fulfillment':
      return "Always positive (+) - seller receiving buyer's payment upon order completion";
    case 'resell_fee_earnings':
      return 'Always positive (+) - earnings from reselling an order';
    default:
      return `Unknown transaction type: ${txType}`;
  }
}

/**
 * Get the appropriate color class for a transaction amount based on its sign
 * @param amount - The transaction amount
 * @returns Tailwind CSS color class
 */
export function getTransactionAmountColor(amount: number): string {
  return amount >= 0 ? 'text-green-600' : 'text-red-600';
}

/**
 * Format transaction amount with proper sign display
 * @param amount - The transaction amount (already with correct sign from backend)
 * @returns Formatted amount string with + or - prefix
 */
export function formatTransactionAmount(amount: number): string {
  const sign = amount >= 0 ? '+' : '-';
  const absoluteAmount = Math.abs(amount);
  return `${sign}${absoluteAmount.toFixed(2)} TON`;
}
