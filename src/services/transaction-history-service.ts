import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';

import type { UserTxEntity } from '@/constants/core.constants';
import { firestore } from '@/root-context';

const USER_TX_HISTORY_COLLECTION = 'user_tx_history';

export interface GetUserTransactionHistoryParams {
  userId: string;
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
}

export interface GetUserTransactionHistoryResult {
  transactions: UserTxEntity[];
  hasMore: boolean;
  lastDoc?: DocumentSnapshot | null;
}

export async function getUserTransactionHistory(
  params: GetUserTransactionHistoryParams,
): Promise<GetUserTransactionHistoryResult> {
  const { userId, limit: pageSize = 20, lastDoc } = params;

  try {
    let q = query(
      collection(firestore, USER_TX_HISTORY_COLLECTION),
      where('user_id', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(pageSize + 1), // Get one extra to check if there are more
    );

    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    const snapshot = await getDocs(q);
    const docs = snapshot.docs;
    const hasMore = docs.length > pageSize;
    const transactionsToReturn = hasMore ? docs.slice(0, pageSize) : docs;

    const transactions: UserTxEntity[] = transactionsToReturn.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    })) as UserTxEntity[];

    const lastDocument = transactionsToReturn[transactionsToReturn.length - 1];

    return {
      transactions,
      hasMore,
      lastDoc: lastDocument || null,
    };
  } catch (error) {
    console.error('Error fetching user transaction history:', error);
    throw error;
  }
}
