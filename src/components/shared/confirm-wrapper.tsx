'use client';

import { cloneElement, isValidElement, useState } from 'react';

import { Button } from '@/components/ui/button';
import { BaseDrawer } from '@/components/ui/drawer/base-drawer';

interface ConfirmWrapperProps {
  children: React.ReactNode;
  message?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  successLabel?: string;
  cancelLabel?: string;
}

export function ConfirmWrapper({
  children,
  message = 'Are you sure you want to proceed with the action?',
  onSuccess,
  onCancel,
  successLabel = 'Yes',
  cancelLabel = 'No',
}: ConfirmWrapperProps) {
  const [open, setOpen] = useState(false);
  const [originalOnClick, setOriginalOnClick] = useState<(() => void) | null>(
    null,
  );

  const handleTriggerClick = (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();
    setOpen(true);
  };

  const handleSuccess = () => {
    if (originalOnClick) {
      originalOnClick();
    }
    onSuccess?.();
    setOpen(false);
  };

  const handleCancel = () => {
    onCancel?.();
    setOpen(false);
  };

  const clonedChildren = isValidElement(children)
    ? cloneElement(children as React.ReactElement<any>, {
        onClick: (event: React.MouseEvent) => {
          const childOnClick = (children as React.ReactElement<any>).props
            .onClick;
          if (childOnClick) {
            setOriginalOnClick(() => childOnClick);
          }
          handleTriggerClick(event);
        },
      })
    : children;

  return (
    <>
      {clonedChildren}

      <BaseDrawer
        className="z-[120]"
        open={open}
        onOpenChange={setOpen}
        zIndex={100}
        height="max-h-[40vh]"
      >
        <div className="space-y-6">
          <div className="text-center">
            <p className="text-[#f5f5f5] text-lg font-medium leading-relaxed">
              {message}
            </p>
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1 rounded-xl py-3 font-semibold border-[#3a4a5c] text-[#708499] hover:bg-[#232e3c]"
            >
              {cancelLabel}
            </Button>
            <Button
              onClick={handleSuccess}
              className="flex-1 rounded-xl py-3 font-semibold bg-[#6ab2f2] hover:bg-[#5a9fd9] text-white"
            >
              {successLabel}
            </Button>
          </div>
        </div>
      </BaseDrawer>
    </>
  );
}
